'use client'

import React, {
  useState, useRef, useEffect, useCallback,
} from 'react';
import Konva from 'konva';
import {
  Stage, Layer,
} from 'react-konva';
import { CanvasSidebar } from './CanvasSidebar';
import { CanvasHeader } from './CanvasHeader';
import { FloatingToolbar } from './FloatingToolbar';
import { cn } from '@/common/utils/helpers';
import { PLATFORM_CANVAS_SIZES } from '@/common/constants';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import toast from 'react-hot-toast';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
  platform?: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
  platform,
}: CanvasEditorProps) => {
  const stageRef = useRef<Konva.Stage>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const [konvaStage, setKonvaStage] = useState<Konva.Stage | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [history, setHistory] = useState<string[]>([]);
  const [historyStep, setHistoryStep] = useState(0);
  const { activeProject } = useProjectContext();

  const handleZoomChange = useCallback((newZoom: number) => {
    setZoomLevel(newZoom);
  }, []);

  const calculateFitToViewZoom = useCallback((canvasWidth: number, canvasHeight: number) => {
    if (!containerRef.current) {
      return 1;
    }

    const container = containerRef.current;
    const containerWidth = container.clientWidth - 80;
    const containerHeight = container.clientHeight - 80;

    const scaleX = containerWidth / canvasWidth;
    const scaleY = containerHeight / canvasHeight;

    return Math.min(scaleX, scaleY, 1.2);
  }, []);

  const fitToView = useCallback(() => {
    if (!konvaStage || !containerRef.current) {
      return;
    }

    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const newZoom = calculateFitToViewZoom(canvasSize.width, canvasSize.height);

    setZoomLevel(newZoom);
    setTimeout(() => {
      if (containerRef.current) {
        const container = containerRef.current;
        const scrollableWidth = container.scrollWidth;
        const scrollableHeight = container.scrollHeight;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        const centerScrollLeft = (scrollableWidth - containerWidth) / 2;
        const centerScrollTop = (scrollableHeight - containerHeight) / 2;

        container.scrollTo({
          left: centerScrollLeft,
          top: centerScrollTop,
          behavior: 'smooth',
        });
      }
    }, 50);
  }, [konvaStage, platform, calculateFitToViewZoom, setZoomLevel]);


  useEffect(() => {
    if (konvaStage) {
      konvaStage.scale({
        x: zoomLevel,
        y: zoomLevel,
      });
      konvaStage.batchDraw();
    }
  }, [zoomLevel, konvaStage]);

  useEffect(() => {
    if (konvaStage && isOpen) {
      setTimeout(() => {
        fitToView();
      }, 100);
    }
  }, [konvaStage, isOpen, fitToView]);

  // Initialize Konva stage
  useEffect(() => {
    if (!stageRef.current || !isOpen) {
      console.log('Canvas initialization skipped:', { stageRef: !!stageRef.current, isOpen });
      return;
    }

    console.log('Initializing canvas...');
    const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
    const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
    const canvasWidth = canvasSize.width;
    const canvasHeight = canvasSize.height;

    const stage = stageRef.current;
    stage.width(canvasWidth);
    stage.height(canvasHeight);

    console.log('Canvas size set to:', { canvasWidth, canvasHeight });
    setKonvaStage(stage);
    console.log('Konva stage set');

    // Initialize history
    setHistory([stage.toJSON()]);
    setHistoryStep(1);

    // Add event listeners for automatic state saving
    const saveStateDelayed = () => {
      setTimeout(() => {
        setHistory(prevHistory => {
          const newHistory = [...prevHistory.slice(0, historyStep)];
          newHistory.push(stage.toJSON());
          setHistoryStep(newHistory.length);
          return newHistory;
        });
      }, 100);
    };

    // Add event listeners to stage for history tracking
    stage.on('dragend', saveStateDelayed);
    stage.on('transformend', saveStateDelayed);

    // Add click handler for object selection
    const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
      // Check if we clicked on an empty area
      if (e.target === stage) {
        // Clear selection if clicking on empty area
        const transformer = stage.findOne('Transformer') as Konva.Transformer;
        if (transformer) {
          transformer.nodes([]);
          stage.batchDraw();
        }
        return;
      }

      // Check if we clicked on a selectable object (not the transformer itself)
      const clickedNode = e.target;
      if (clickedNode.getClassName() === 'Transformer') {
        return;
      }

      // Find or create transformer
      const layer = clickedNode.getLayer();
      if (!layer) return;

      let transformer = layer.findOne('Transformer') as Konva.Transformer;
      if (!transformer) {
        transformer = new Konva.Transformer();
        layer.add(transformer);
      }

      // Select the clicked object
      transformer.nodes([clickedNode]);
      stage.batchDraw();
    };

    stage.on('click', handleStageClick);

    // Handle initial image if provided
    if (initialImage) {
      const imageObj = new Image();
      imageObj.crossOrigin = 'anonymous';
      imageObj.onload = () => {
        let layer = stage.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          stage.add(layer);
        }

        const canvasWidth = stage.width();
        const canvasHeight = stage.height();
        const imgWidth = imageObj.width;
        const imgHeight = imageObj.height;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        const konvaImage = new Konva.Image({
          image: imageObj,
          x: (canvasWidth - imgWidth * scale) / 2,
          y: (canvasHeight - imgHeight * scale) / 2,
          scaleX: scale,
          scaleY: scale,
          draggable: true,
        });

        layer.add(konvaImage);
        layer.batchDraw();

        // Save state after adding initial image
        setTimeout(() => {
          setHistory(prevHistory => {
            const newHistory = [...prevHistory.slice(0, historyStep)];
            newHistory.push(stage.toJSON());
            setHistoryStep(newHistory.length);
            return newHistory;
          });
        }, 100);
      };
      imageObj.src = initialImage;
    }

    return () => {
      stage.off('dragend', saveStateDelayed);
      stage.off('transformend', saveStateDelayed);
      stage.off('click', handleStageClick);
    };
  }, [isOpen, initialImage, platform, historyStep]);

  // History management functions

  const undo = useCallback(() => {
    setHistoryStep(prevStep => {
      if (prevStep > 1 && konvaStage) {
        const newStep = prevStep - 1;
        setHistory(prevHistory => {
          const state = prevHistory[newStep - 1];
          if (state) {
            konvaStage.destroy();
            const newStage = Konva.Node.create(state, stageRef.current?.container());
            setKonvaStage(newStage);
          }
          return prevHistory;
        });
        return newStep;
      }
      return prevStep;
    });
  }, [konvaStage]);

  const redo = useCallback(() => {
    setHistoryStep(prevStep => {
      setHistory(prevHistory => {
        if (prevStep < prevHistory.length && konvaStage) {
          const state = prevHistory[prevStep];
          if (state) {
            konvaStage.destroy();
            const newStage = Konva.Node.create(state, stageRef.current?.container());
            setKonvaStage(newStage);
            return prevHistory;
          }
        }
        return prevHistory;
      });
      return prevStep < history.length ? prevStep + 1 : prevStep;
    });
  }, [konvaStage, history.length]);

  // useEffect(() => {
  //   if (!stageRef.current || !isOpen) {
  //     return;
  //   }

  //   const platformKey = platform?.toLowerCase() as keyof typeof PLATFORM_CANVAS_SIZES;
  //   const canvasSize = PLATFORM_CANVAS_SIZES[platformKey] || PLATFORM_CANVAS_SIZES.default;
  //   const canvasWidth = canvasSize.width;
  //   const canvasHeight = canvasSize.height;

  //   const stage = stageRef.current;
  //   stage.width(canvasWidth);
  //   stage.height(canvasHeight);

  //   setKonvaStage(stage);

  //   // Initialize history
  //   setHistory([stage.toJSON()]);
  //   setHistoryStep(1);

  //   // Add event listeners for automatic state saving
  //   const saveStateDelayed = () => {
  //     setTimeout(() => {
  //       setHistory(prevHistory => {
  //         const newHistory = [...prevHistory.slice(0, historyStep)];
  //         newHistory.push(stage.toJSON());
  //         setHistoryStep(newHistory.length);
  //         return newHistory;
  //       });
  //     }, 100);
  //   };

  //   // Add event listeners to stage for history tracking
  //   stage.on('dragend', saveStateDelayed);
  //   stage.on('transformend', saveStateDelayed);

  //   // Save initial state
  //   setTimeout(() => {
  //     setHistory(prevHistory => {
  //       const newHistory = [...prevHistory.slice(0, 1)];
  //       newHistory.push(stage.toJSON());
  //       setHistoryStep(newHistory.length);
  //       return newHistory;
  //     });
  //   }, 100);

  //   // Konva event handlers for panning and interaction
  //   let isDragging = false;
  //   let lastPointerPosition = {
  //     x: 0,
  //     y: 0,
  //   };

  //   stage.on('mousedown touchstart', (e) => {
  //     if (e.evt.altKey) {
  //       isDragging = true;
  //       const pos = stage.getPointerPosition();
  //       if (pos) {
  //         lastPointerPosition = pos;
  //       }
  //     }
  //   });

  //   stage.on('mousemove touchmove', (e) => {
  //     if (!isDragging) {
  //       return;
  //     }

  //     e.evt.preventDefault();
  //     const pos = stage.getPointerPosition();
  //     if (!pos) {
  //       return;
  //     }

  //     const dx = pos.x - lastPointerPosition.x;
  //     const dy = pos.y - lastPointerPosition.y;

  //     const newPos = {
  //       x: stage.x() + dx,
  //       y: stage.y() + dy,
  //     };

  //     stage.position(newPos);
  //     stage.batchDraw();
  //     lastPointerPosition = pos;
  //   });

  //   stage.on('mouseup touchend', () => {
  //     isDragging = false;
  //   });

  //   const handleKeyDown = (e: KeyboardEvent) => {
  //     if (e.key === 'Delete') {
  //       const transformer = stage.findOne('Transformer') as Konva.Transformer;
  //       if (transformer) {
  //         const nodes = transformer.nodes();
  //         nodes.forEach((node: Konva.Node) => node.destroy());
  //         transformer.nodes([]);
  //         stage.batchDraw();
  //         // Save state directly without dependency
  //         setHistory(prevHistory => {
  //           setHistoryStep(prevStep => {
  //             const newHistory = [...prevHistory.slice(0, prevStep)];
  //             newHistory.push(stage.toJSON());
  //             return newHistory.length;
  //           });
  //           return [...prevHistory.slice(0, historyStep), stage.toJSON()];
  //         });
  //       }
  //     }

  //     // Undo/Redo keyboard shortcuts will be handled by the FloatingToolbar
  //   };

  //   document.addEventListener('keydown', handleKeyDown);

  //   if (initialImage) {
  //     const imageObj = new Image();
  //     imageObj.crossOrigin = 'anonymous';
  //     imageObj.onload = () => {
  //       let layer = stage.findOne('Layer') as Konva.Layer;
  //       if (!layer) {
  //         layer = new Konva.Layer();
  //         stage.add(layer);
  //       }

  //       const canvasWidth = stage.width();
  //       const canvasHeight = stage.height();
  //       const imgWidth = imageObj.width;
  //       const imgHeight = imageObj.height;

  //       const scaleX = canvasWidth / imgWidth;
  //       const scaleY = canvasHeight / imgHeight;
  //       const scale = Math.min(scaleX, scaleY);

  //       const konvaImage = new Konva.Image({
  //         image: imageObj,
  //         x: (canvasWidth - imgWidth * scale) / 2,
  //         y: (canvasHeight - imgHeight * scale) / 2,
  //         scaleX: scale,
  //         scaleY: scale,
  //         draggable: true,
  //       });

  //       layer.add(konvaImage);
  //       layer.batchDraw();
  //       // Save state directly without dependency
  //       setTimeout(() => {
  //         setHistory(prevHistory => {
  //           setHistoryStep(prevStep => {
  //             const newHistory = [...prevHistory.slice(0, prevStep)];
  //             newHistory.push(stage.toJSON());
  //             return newHistory.length;
  //           });
  //           return [...prevHistory.slice(0, historyStep), stage.toJSON()];
  //         });
  //       }, 100);
  //     };
  //     imageObj.src = initialImage;
  //   }

  //   return () => {
  //     document.removeEventListener('keydown', handleKeyDown);
  //     stage.destroy();
  //   };
  // }, [isOpen, initialImage, platform, historyStep]);

  const handleSaveDesign = async () => {
    if (!konvaStage) {
      console.error('Canvas not available');
      return;
    }

    if (!activeProject?.project_id) {
      console.error('No active project');
      toast.error('No active project found');
      return;
    }

    try {
      const dataURL = konvaStage.toDataURL({
        mimeType: 'image/png',
        quality: 1,
        pixelRatio: 1,
      });

      const response = await fetch(dataURL);
      const blob = await response.blob();
      const timestamp = Date.now();
      const fileName = `canvas-design-${timestamp}.png`;
      const file = new File([blob], fileName, { type: 'image/png' });

      const formData = new FormData();
      formData.append('image', file);
      formData.append('planId', planId || 'canvas-save');

      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const uploadEndpoint = `${baseUrl}/agents/${agentId}/upload-canvas-image`;

      const uploadResponse = await fetch(uploadEndpoint, {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload canvas image');
      }

      const uploadResult = await uploadResponse.json();

      if (uploadResult.success && uploadResult.filepath) {
        await projectImageStorage.addCreationImage(
          activeProject.project_id,
          agentId,
          uploadResult.filepath,
          fileName,
          planId,
          'Canvas Design',
        );

        window.dispatchEvent(new CustomEvent('projectImagesUpdated', {
          detail: { projectId: activeProject.project_id },
        }));

        onSave(uploadResult.filepath);
        toast.success('Canvas design saved successfully!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Error saving canvas design:', error);
      toast.error('Failed to save canvas design');
      onSave('');
    }
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-0 left-0 right-0 bottom-0 h-[calc(100vh)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasHeader
        onSaveDesign={handleSaveDesign}
        canvas={konvaStage}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={konvaStage}
            agentId={agentId}
            planId={planId}
            containerRef={canvasContainerRef}
            zoomLevel={zoomLevel}
            onClose={onClose}
          />
        </div>
        <div className="flex-1 bg-neutral-800 flex flex-col overflow-hidden">
          <div
            ref={canvasContainerRef}
            className="flex-1 w-full overflow-auto scroll-smooth"
          >
            <div
              className="flex items-center justify-center"
              // style={{
              //   minHeight: `${Math.max(100, zoomLevel * 120)}%`,
              //   minWidth: `${Math.max(100, zoomLevel * 120)}%`,
              //   padding: `${Math.max(32, zoomLevel * 40)}px`,
              // }}
            >
              <div
                className="bg-violets-are-blue/5 rounded-xl p-2 md:p-4 w-auto shadow-lg transition-transform duration-200"
                // style={{
                //   transform: `scale(${zoomLevel})`,
                //   transformOrigin: 'center center',
                // }}
              >
                <Stage
                  ref={stageRef}
                  width={1200}
                  height={1200}
                  className="border border-gray-200 rounded block"
                >
                  <Layer />
                </Stage>
              </div>
            </div>
          </div>
          <div className="p-2 md:p-4 text-center text-gray-500 text-xs">
            <div className="flex flex-col items-center gap-1">
              <p>Select layer for more options | Double-click text to edit inline | Delete key to remove selected objects</p>
            </div>
          </div>
        </div>
      </div>

      <FloatingToolbar
        canvas={konvaStage}
        zoomLevel={zoomLevel}
        onZoomChange={handleZoomChange}
        onFitToView={fitToView}
        onUndo={undo}
        onRedo={redo}
        canUndo={historyStep > 1}
        canRedo={historyStep < history.length}
      />
    </div>
  );
};
